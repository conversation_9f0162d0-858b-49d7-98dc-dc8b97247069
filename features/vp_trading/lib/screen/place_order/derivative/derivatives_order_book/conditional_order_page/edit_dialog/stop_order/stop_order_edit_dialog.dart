import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/place_order/awaiting/widget/choice_equal_buy_button.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/enum/condition_order_type_fu_enum.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class StopOrderEditDialog extends StatefulWidget {
  const StopOrderEditDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<StopOrderEditDialog> createState() => _StopOrderEditDialogState();
}

class _StopOrderEditDialogState extends State<StopOrderEditDialog> {
  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final _priceFocusNode = FocusNode();
  late final TextEditingController _priceController;

  @override
  void initState() {
    super.initState();

    // Initialize price controller with existing activation price
    _priceController = TextEditingController(
      text: widget.model?.activePrice?.toString() ?? '',
    );

    _priceFocusNode.addListener(_focusListener);

    // Initialize validation cubit with existing data if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.model?.activePrice != null) {
        context.read<DerivativeValidateOrderCubit>().onChangeActivePrice(
          widget.model!.activePrice!.toString(),
        );
      }

      // Set activation type from model
      if (widget.model?.activeType != null) {
        final activationType =
            widget.model!.activeType == 'UP'
                ? ActivationConditionsType.greaterThan
                : ActivationConditionsType.lessThan;
        context.read<DerivativeValidateOrderCubit>().setActivationConditions(
          activationType,
        );
      }
    });
  }

  void _focusListener() {
    if (_priceFocusNode.hasFocus) {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.priceActive,
      );
    } else {
      context.read<DerivativeValidateOrderCubit>().focusField(
        FocusKeyboard.none,
      );
    }
  }

  @override
  void dispose() {
    _priceFocusNode.dispose();
    _priceBlink.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DerivativeValidateOrderCubit(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dialog Title
          Text(
            ConditionOrderTypeFuEnum.sto.titleEdit ?? 'Sửa lệnh chờ',
            style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
          ),
          const SizedBox(height: 24),

          // Stop Order Content
          _buildStopOrderContent(),

          const SizedBox(height: 24),

          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildStopOrderContent() {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentActivePrice != current.currentActivePrice,
          listener: (context, state) {
            if (state.currentActivePrice != null) {
              _priceController.text = state.currentActivePrice!;
              _priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: _priceController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.focusKeyboard != current.focusKeyboard,
          listener: (context, state) {
            if (state.focusKeyboard == FocusKeyboard.priceActive) {
              _priceFocusNode.requestFocus();
            }
          },
        ),
      ],
      child: BlocBuilder<
        DerivativeValidateOrderCubit,
        DerivativeValidateOrderState
      >(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    VPTradingLocalize.current.derivative_activation_conditions,
                    style: context.textStyle.body14?.copyWith(
                      color: vpColor.textPrimary,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const ButtonLessThan(),
                  const SizedBox(width: 4),
                  const ButtonGreaterThan(),
                  const SizedBox(width: 4),
                  Expanded(
                    child: ColoredBox(
                      color: vpColor.backgroundElevation0,
                      child: InputFieldBox(
                        onTapField: () {
                          if (state.sessionType != null) {
                            context
                                .read<DerivativeValidateOrderCubit>()
                                .clearSession();
                            _priceFocusNode.requestFocus();
                          }
                        },
                        isError:
                            state.errorActivePrice.isError &&
                            _priceController.text.isNotEmpty,
                        sessionValue: state.sessionType?.name.toUpperCase(),
                        controller: _priceController,
                        hintText: 'Giá',
                        onChange: (value) {
                          context
                              .read<DerivativeValidateOrderCubit>()
                              .onChangeActivePrice(value);
                        },
                        focusNode: _priceFocusNode,
                        onTap: (increase) {
                          context.read<DerivativeValidateOrderCubit>().priceTap(
                            text: _priceController.text,
                            increase: increase,
                            activation: true,
                          );
                        },
                        inputFormatters: [
                          removeZeroStartInputFormatter,
                          LengthLimitingTextInputFormatter(8),
                          ...priceInputFormatter,
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              BlocBuilder<
                DerivativeValidateOrderCubit,
                DerivativeValidateOrderState
              >(
                buildWhen:
                    (previous, current) =>
                        previous.errorActivePrice != current.errorActivePrice ||
                        previous.currentActivePrice !=
                            current.currentActivePrice,
                builder: (context, state) {
                  return InputFieldError(
                    errorMessage: state.errorActivePrice.message,
                    text: _priceController.text,
                    isShake: true,
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      builder: (context, validateState) {
        return BlocBuilder<
          DerivativeConditionOrderEditCubit,
          DerivativeConditionOrderEditState
        >(
          builder: (context, editState) {
            final isValid =
                !validateState.errorActivePrice.isError &&
                _priceController.text.isNotEmpty;

            return Row(
              children: [
                Expanded(
                  child: VpsButton.teriatySmall(
                    title: 'Hủy',
                    onPressed:
                        editState.canPerformAction
                            ? () {
                              context.pop();
                            }
                            : null,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: VpsButton.primarySmall(
                    title: 'Xác nhận',
                    disabled: !isValid || !editState.canPerformAction,
                    onPressed:
                        isValid && editState.canPerformAction
                            ? () {
                              _handleConfirm(context, validateState);
                            }
                            : null,
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _handleConfirm(
    BuildContext context,
    DerivativeValidateOrderState validateState,
  ) {
    if (widget.model == null) return;

    final request = ConditionOrderRequestModel(
      orderId: widget.model!.orderId,
      accountId: widget.model!.accountId ?? '',
      orderType: 'STO',
      conditionInfo: ConditionInfo(
        symbol: widget.model!.symbol ?? '',
        qty: widget.model!.qty ?? 0,
        side: widget.model!.side ?? '',
        type: 'STO',
        price: widget.model!.price,
        fromDate: widget.model!.fromDate ?? '',
        toDate: widget.model!.toDate ?? '',
        activePrice: double.tryParse(_priceController.text),
        activeType: validateState.activationType.toParamRequest(),
      ),
    );

    context
        .read<DerivativeConditionOrderEditCubit>()
        .editDerivativeConditionOrder(request: request);
  }
}

class ButtonGreaterThan extends StatelessWidget {
  const ButtonGreaterThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icGreaterThanEqual.path,
          isFocus: activationType == ActivationConditionsType.greaterThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(
                    ActivationConditionsType.greaterThan,
                  ),
        );
      },
    );
  }
}

class ButtonLessThan extends StatelessWidget {
  const ButtonLessThan({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icLessThanEqual.path,
          isFocus: activationType == ActivationConditionsType.lessThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(ActivationConditionsType.lessThan),
        );
      },
    );
  }
}
